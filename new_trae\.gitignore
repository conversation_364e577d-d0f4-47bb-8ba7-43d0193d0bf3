# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Coverage
coverage/
.nyc_output/

# Temporary files
tmp/
temp/

# Configuration files with secrets
trae_config.json
config.json

# Trajectory files
trajectories/
*.trajectory.json

# Test files
*.test.js
*.spec.js

# TypeScript
*.tsbuildinfo

# Local storage
.trae-agent/
