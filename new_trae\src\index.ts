/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

/**
 * Trae Agent - TypeScript implementation
 * LLM-based agent for general purpose software engineering tasks
 */

export { Agent } from './agent/base';
export { TraeAgent } from './agent/trae-agent';
export { LLMClient, LLMProvider } from './utils/llm-client';
export { Tool, ToolExecutor } from './tools/base';
export { Config, ModelParameters } from './utils/config';
export * from './types';

export const VERSION = '0.1.0';
