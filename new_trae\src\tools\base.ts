/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import {
  ToolParameter,
  ToolCallArguments,
  ToolCall,
  ToolExecResult,
  ToolResult,
  ToolInputSchema,
} from '../types/tool';

/**
 * Tool error class
 */
export class ToolError extends Error {
  constructor(message: string, public readonly code?: number) {
    super(message);
    this.name = 'ToolError';
  }
}

/**
 * Base class for all tools
 */
export abstract class Tool {
  protected modelProvider?: string;

  constructor(modelProvider?: string) {
    this.modelProvider = modelProvider;
  }

  /**
   * Get the model provider
   */
  getModelProvider(): string | undefined {
    return this.modelProvider;
  }

  /**
   * Get the tool name
   */
  abstract getName(): string;

  /**
   * Get the tool description
   */
  abstract getDescription(): string;

  /**
   * Get the tool parameters
   */
  abstract getParameters(): ToolParameter[];

  /**
   * Execute the tool with given arguments
   */
  abstract execute(arguments: ToolCallArguments): Promise<ToolExecResult>;

  /**
   * Get input schema for LLM
   */
  getInputSchema(): ToolInputSchema {
    const parameters = this.getParameters();
    const properties: Record<string, unknown> = {};
    const required: string[] = [];

    for (const param of parameters) {
      properties[param.name] = {
        type: param.type,
        description: param.description,
        ...(param.enum && { enum: param.enum }),
        ...(param.properties && { properties: param.properties }),
        ...(param.items && { items: param.items }),
      };

      if (param.required) {
        required.push(param.name);
      }
    }

    return {
      type: 'object',
      properties,
      required,
      additionalProperties: false,
    };
  }

  /**
   * Validate tool arguments
   */
  protected validateArguments(arguments: ToolCallArguments): void {
    const parameters = this.getParameters();
    const requiredParams = parameters.filter(p => p.required);

    for (const param of requiredParams) {
      if (!(param.name in arguments)) {
        throw new ToolError(`Missing required parameter: ${param.name}`);
      }
    }

    // Basic type validation
    for (const param of parameters) {
      const value = arguments[param.name];
      if (value !== undefined && value !== null) {
        this.validateParameterType(param, value);
      }
    }
  }

  private validateParameterType(param: ToolParameter, value: unknown): void {
    switch (param.type) {
      case 'string':
        if (typeof value !== 'string') {
          throw new ToolError(`Parameter ${param.name} must be a string`);
        }
        if (param.enum && !param.enum.includes(value)) {
          throw new ToolError(`Parameter ${param.name} must be one of: ${param.enum.join(', ')}`);
        }
        break;
      case 'number':
        if (typeof value !== 'number') {
          throw new ToolError(`Parameter ${param.name} must be a number`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new ToolError(`Parameter ${param.name} must be a boolean`);
        }
        break;
      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          throw new ToolError(`Parameter ${param.name} must be an object`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          throw new ToolError(`Parameter ${param.name} must be an array`);
        }
        break;
    }
  }
}

/**
 * Tool executor for running tools
 */
export class ToolExecutor {
  private tools: Map<string, Tool> = new Map();

  constructor(tools: Tool[]) {
    for (const tool of tools) {
      this.tools.set(tool.getName(), tool);
    }
  }

  /**
   * Add a tool to the executor
   */
  addTool(tool: Tool): void {
    this.tools.set(tool.getName(), tool);
  }

  /**
   * Remove a tool from the executor
   */
  removeTool(toolName: string): void {
    this.tools.delete(toolName);
  }

  /**
   * Get a tool by name
   */
  getTool(toolName: string): Tool | undefined {
    return this.tools.get(toolName);
  }

  /**
   * Get all available tools
   */
  getTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Execute tool calls sequentially
   */
  async sequentialToolCall(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];

    for (const toolCall of toolCalls) {
      const result = await this.executeToolCall(toolCall);
      results.push(result);
    }

    return results;
  }

  /**
   * Execute tool calls in parallel
   */
  async parallelToolCall(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const promises = toolCalls.map(toolCall => this.executeToolCall(toolCall));
    return Promise.all(promises);
  }

  /**
   * Execute a single tool call
   */
  private async executeToolCall(toolCall: ToolCall): Promise<ToolResult> {
    const tool = this.tools.get(toolCall.name);
    if (!tool) {
      return {
        callId: toolCall.callId,
        name: toolCall.name,
        result: {
          error: `Tool '${toolCall.name}' not found`,
          errorCode: -1,
        },
      };
    }

    try {
      const result = await tool.execute(toolCall.arguments);
      return {
        callId: toolCall.callId,
        name: toolCall.name,
        result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = error instanceof ToolError ? error.code : -1;

      return {
        callId: toolCall.callId,
        name: toolCall.name,
        result: {
          error: errorMessage,
          errorCode,
        },
      };
    }
  }
}
