/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { spawn, ChildProcess } from 'child_process';
import * as os from 'os';
import { Tool, ToolError } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult } from '../types/tool';
import { DEFAULT_TOOL_TIMEOUT } from '../utils/constants';

/**
 * Bash session for persistent command execution
 */
class BashSession {
  private process?: ChildProcess;
  private started = false;
  private timedOut = false;
  private readonly command: string;
  private readonly outputDelay = 200; // milliseconds
  private readonly timeout = DEFAULT_TOOL_TIMEOUT;
  private readonly sentinel = '<<exit>>';

  constructor() {
    this.command = os.platform() === 'win32' ? 'cmd.exe' : '/bin/bash';
  }

  async start(): Promise<void> {
    if (this.started) {
      return;
    }

    this.process = spawn(this.command, [], {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: false,
    });

    this.started = true;
  }

  stop(): void {
    if (!this.started || !this.process) {
      throw new ToolError('Session has not started.');
    }

    if (this.process.exitCode !== null) {
      return;
    }

    this.process.kill();
  }

  async run(command: string): Promise<ToolExecResult> {
    if (!this.started || !this.process) {
      throw new ToolError('Session has not started.');
    }

    if (this.process.exitCode !== null) {
      return {
        error: `bash has exited with returncode ${this.process.exitCode}. tool must be restarted.`,
        errorCode: -1,
      };
    }

    if (this.timedOut) {
      throw new ToolError(
        `timed out: bash has not returned in ${this.timeout / 1000} seconds and must be restarted`
      );
    }

    return new Promise((resolve) => {
      let output = '';
      let errorOutput = '';
      let timeoutId: NodeJS.Timeout;

      const cleanup = () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        this.process?.stdout?.removeAllListeners('data');
        this.process?.stderr?.removeAllListeners('data');
      };

      // Set timeout
      timeoutId = setTimeout(() => {
        this.timedOut = true;
        cleanup();
        resolve({
          error: `Command timed out after ${this.timeout / 1000} seconds`,
          errorCode: -1,
        });
      }, this.timeout);

      // Handle stdout
      this.process.stdout?.on('data', (data: Buffer) => {
        output += data.toString();
      });

      // Handle stderr
      this.process.stderr?.on('data', (data: Buffer) => {
        errorOutput += data.toString();
      });

      // Send command with sentinel
      const fullCommand = `${command}\necho "${this.sentinel}"\n`;
      this.process.stdin?.write(fullCommand);

      // Wait for output and check for sentinel
      const checkOutput = () => {
        if (output.includes(this.sentinel)) {
          cleanup();
          
          // Remove sentinel from output
          const cleanOutput = output.replace(new RegExp(`\\n?${this.sentinel}\\n?`, 'g'), '').trim();
          
          if (errorOutput.trim()) {
            resolve({
              output: cleanOutput,
              error: errorOutput.trim(),
            });
          } else {
            resolve({
              output: cleanOutput,
            });
          }
        } else {
          setTimeout(checkOutput, this.outputDelay);
        }
      };

      setTimeout(checkOutput, this.outputDelay);
    });
  }
}

/**
 * Tool for executing bash commands
 */
export class BashTool extends Tool {
  private session?: BashSession;

  getName(): string {
    return 'bash';
  }

  getDescription(): string {
    return `Run commands in a bash shell
* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
* You have access to a mirror of common linux and python packages via apt and pip.
* State is persistent across command calls and discussions with the user.
* To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.
* Please avoid commands that may produce a very large amount of output.
* Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background.`;
  }

  getParameters(): ToolParameter[] {
    const restartRequired = this.modelProvider === 'openai';

    return [
      {
        name: 'command',
        type: 'string',
        description: 'The bash command to run.',
        required: true,
      },
      {
        name: 'restart',
        type: 'boolean',
        description: 'Set to true to restart the bash session.',
        required: restartRequired,
      },
    ];
  }

  async execute(arguments: ToolCallArguments): Promise<ToolExecResult> {
    this.validateArguments(arguments);

    if (arguments.restart) {
      if (this.session) {
        this.session.stop();
      }
      this.session = new BashSession();
      await this.session.start();

      return {
        output: 'tool has been restarted.',
      };
    }

    if (!this.session) {
      try {
        this.session = new BashSession();
        await this.session.start();
      } catch (error) {
        return {
          error: `Error starting bash session: ${error}`,
          errorCode: -1,
        };
      }
    }

    const command = arguments.command as string;
    if (!command) {
      return {
        error: `No command provided for the ${this.getName()} tool`,
        errorCode: -1,
      };
    }

    try {
      return await this.session.run(command);
    } catch (error) {
      return {
        error: `Error running bash command: ${error}`,
        errorCode: -1,
      };
    }
  }
}
