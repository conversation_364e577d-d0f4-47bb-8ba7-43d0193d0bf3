/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolError } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult } from '../types/tool';
import { MAX_FILE_SIZE, SUPPORTED_CODE_EXTENSIONS } from '../utils/constants';

/**
 * Edit tool sub-commands
 */
const EDIT_TOOL_SUB_COMMANDS = ['view', 'create', 'str_replace', 'insert'] as const;
type EditToolSubCommand = typeof EDIT_TOOL_SUB_COMMANDS[number];

/**
 * Tool for file and directory manipulation
 */
export class EditTool extends Tool {
  getName(): string {
    return 'str_replace_based_edit_tool';
  }

  getDescription(): string {
    return `File and directory manipulation tool with persistent state.

Operations:
- view: Display file contents with line numbers, or list directory contents up to 2 levels deep
- create: Create new files (fails if file already exists)
- str_replace: Replace exact string matches in files (must be unique)
- insert: Insert text after a specified line number

Key features:
- Requires absolute paths (e.g., /repo/file.py)
- String replacements must match exactly, including whitespace
- Supports line range viewing for large files`;
  }

  getParameters(): ToolParameter[] {
    return [
      {
        name: 'command',
        type: 'string',
        description: 'The command to execute',
        required: true,
        enum: [...EDIT_TOOL_SUB_COMMANDS],
      },
      {
        name: 'path',
        type: 'string',
        description: 'The file or directory path',
        required: true,
      },
      {
        name: 'file_text',
        type: 'string',
        description: 'The content for create command',
        required: false,
      },
      {
        name: 'old_str',
        type: 'string',
        description: 'The string to replace for str_replace command',
        required: false,
      },
      {
        name: 'new_str',
        type: 'string',
        description: 'The replacement string for str_replace command',
        required: false,
      },
      {
        name: 'insert_line',
        type: 'number',
        description: 'The line number after which to insert for insert command',
        required: false,
      },
      {
        name: 'new_str',
        type: 'string',
        description: 'The text to insert for insert command',
        required: false,
      },
      {
        name: 'view_range',
        type: 'array',
        description: 'Line range for view command [start, end]',
        required: false,
        items: {
          name: 'line',
          type: 'number',
          description: 'Line number',
          required: true,
        },
      },
    ];
  }

  async execute(arguments: ToolCallArguments): Promise<ToolExecResult> {
    this.validateArguments(arguments);

    const command = arguments.command as EditToolSubCommand;
    const filePath = arguments.path as string;

    if (!EDIT_TOOL_SUB_COMMANDS.includes(command)) {
      return {
        error: `Unrecognized command ${command}. The allowed commands for the ${this.getName()} tool are: ${EDIT_TOOL_SUB_COMMANDS.join(', ')}`,
        errorCode: -1,
      };
    }

    try {
      this.validatePath(command, filePath);

      switch (command) {
        case 'view':
          return await this.viewHandler(arguments, filePath);
        case 'create':
          return this.createHandler(arguments, filePath);
        case 'str_replace':
          return this.strReplaceHandler(arguments, filePath);
        case 'insert':
          return this.insertHandler(arguments, filePath);
        default:
          return {
            error: `Unknown command: ${command}`,
            errorCode: -1,
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        error: errorMessage,
        errorCode: -1,
      };
    }
  }

  private validatePath(command: EditToolSubCommand, filePath: string): void {
    if (!path.isAbsolute(filePath)) {
      throw new ToolError(`Path must be absolute: ${filePath}`);
    }

    if (command !== 'create' && command !== 'view') {
      if (!fs.existsSync(filePath)) {
        throw new ToolError(`File does not exist: ${filePath}`);
      }
    }
  }

  private async viewHandler(arguments: ToolCallArguments, filePath: string): Promise<ToolExecResult> {
    if (!fs.existsSync(filePath)) {
      return {
        error: `Path does not exist: ${filePath}`,
        errorCode: -1,
      };
    }

    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      return this.viewDirectory(filePath);
    } else {
      return this.viewFile(filePath, arguments.view_range as number[] | undefined);
    }
  }

  private viewDirectory(dirPath: string): ToolExecResult {
    try {
      const items: string[] = [];
      
      const addItems = (currentPath: string, level: number, prefix: string = '') => {
        if (level > 2) return;
        
        const entries = fs.readdirSync(currentPath, { withFileTypes: true });
        
        for (const entry of entries) {
          if (entry.name.startsWith('.')) continue;
          
          const fullPath = path.join(currentPath, entry.name);
          const relativePath = path.relative(dirPath, fullPath);
          
          if (entry.isDirectory()) {
            items.push(`${prefix}${relativePath}/`);
            addItems(fullPath, level + 1, prefix + '  ');
          } else {
            items.push(`${prefix}${relativePath}`);
          }
        }
      };
      
      addItems(dirPath, 0);
      
      return {
        output: `Directory contents of ${dirPath}:\n${items.join('\n')}`,
      };
    } catch (error) {
      return {
        error: `Error reading directory: ${error}`,
        errorCode: -1,
      };
    }
  }

  private viewFile(filePath: string, viewRange?: number[]): ToolExecResult {
    try {
      const stats = fs.statSync(filePath);
      
      if (stats.size > MAX_FILE_SIZE) {
        return {
          error: `File too large (${stats.size} bytes). Maximum size is ${MAX_FILE_SIZE} bytes.`,
          errorCode: -1,
        };
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');

      let startLine = 1;
      let endLine = lines.length;

      if (viewRange && viewRange.length === 2) {
        startLine = Math.max(1, viewRange[0]);
        endLine = Math.min(lines.length, viewRange[1]);
      }

      const numberedLines = lines
        .slice(startLine - 1, endLine)
        .map((line, index) => `${String(startLine + index).padStart(4)}: ${line}`)
        .join('\n');

      return {
        output: `File: ${filePath}\n${numberedLines}`,
      };
    } catch (error) {
      return {
        error: `Error reading file: ${error}`,
        errorCode: -1,
      };
    }
  }

  private createHandler(arguments: ToolCallArguments, filePath: string): ToolExecResult {
    const fileText = arguments.file_text as string;
    
    if (typeof fileText !== 'string') {
      return {
        error: 'Parameter `file_text` is required and must be a string for command: create',
        errorCode: -1,
      };
    }

    if (fs.existsSync(filePath)) {
      return {
        error: `File already exists: ${filePath}`,
        errorCode: -1,
      };
    }

    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, fileText, 'utf-8');
      
      return {
        output: `File created successfully at: ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error creating file: ${error}`,
        errorCode: -1,
      };
    }
  }

  private strReplaceHandler(arguments: ToolCallArguments, filePath: string): ToolExecResult {
    const oldStr = arguments.old_str as string;
    const newStr = arguments.new_str as string;

    if (typeof oldStr !== 'string' || typeof newStr !== 'string') {
      return {
        error: 'Parameters `old_str` and `new_str` are required and must be strings for command: str_replace',
        errorCode: -1,
      };
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      
      const occurrences = (content.match(new RegExp(this.escapeRegExp(oldStr), 'g')) || []).length;
      
      if (occurrences === 0) {
        return {
          error: `String not found in file: ${oldStr}`,
          errorCode: -1,
        };
      }
      
      if (occurrences > 1) {
        return {
          error: `String appears ${occurrences} times in file. Replacement must be unique.`,
          errorCode: -1,
        };
      }

      const newContent = content.replace(oldStr, newStr);
      fs.writeFileSync(filePath, newContent, 'utf-8');

      return {
        output: `String replaced successfully in: ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error replacing string: ${error}`,
        errorCode: -1,
      };
    }
  }

  private insertHandler(arguments: ToolCallArguments, filePath: string): ToolExecResult {
    const insertLine = arguments.insert_line as number;
    const newStr = arguments.new_str as string;

    if (typeof insertLine !== 'number' || typeof newStr !== 'string') {
      return {
        error: 'Parameters `insert_line` and `new_str` are required for command: insert',
        errorCode: -1,
      };
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');

      if (insertLine < 0 || insertLine > lines.length) {
        return {
          error: `Invalid line number: ${insertLine}. File has ${lines.length} lines.`,
          errorCode: -1,
        };
      }

      lines.splice(insertLine, 0, newStr);
      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf-8');

      return {
        output: `Text inserted successfully at line ${insertLine} in: ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error inserting text: ${error}`,
        errorCode: -1,
      };
    }
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}
