/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { Tool } from './base';
import { BashTool } from './bash-tool';
import { EditTool } from './edit-tool';
import { JSONEditTool } from './json-edit-tool';
import { SequentialThinkingTool } from './sequential-thinking-tool';
import { TaskDoneTool } from './task-done-tool';

export * from './base';
export * from './bash-tool';
export * from './edit-tool';
export * from './json-edit-tool';
export * from './sequential-thinking-tool';
export * from './task-done-tool';

/**
 * Tool registry for all available tools
 */
export const toolsRegistry: Record<string, new (modelProvider?: string) => Tool> = {
  bash: BashTool,
  str_replace_based_edit_tool: EditTool,
  json_edit_tool: JSONEditTool,
  sequentialthinking: SequentialThinkingTool,
  task_done: TaskDoneTool,
};

/**
 * Default tool names for TraeAgent
 */
export const TRAE_AGENT_TOOL_NAMES = [
  'str_replace_based_edit_tool',
  'sequentialthinking',
  'json_edit_tool',
  'task_done',
  'bash',
];

/**
 * Create tools from tool names
 */
export function createTools(toolNames: string[], modelProvider?: string): Tool[] {
  const tools: Tool[] = [];

  for (const toolName of toolNames) {
    const ToolClass = toolsRegistry[toolName];
    if (ToolClass) {
      tools.push(new ToolClass(modelProvider));
    } else {
      console.warn(`Unknown tool: ${toolName}`);
    }
  }

  return tools;
}
