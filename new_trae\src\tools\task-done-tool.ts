/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { Tool } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult } from '../types/tool';

/**
 * Tool to mark a task as done
 */
export class TaskDoneTool extends Tool {
  getName(): string {
    return 'task_done';
  }

  getDescription(): string {
    return 'Report the completion of the task. Note that you cannot call this tool before any verification is done. You can write reproduce / test script to verify your solution.';
  }

  getParameters(): ToolParameter[] {
    return [];
  }

  async execute(_arguments: ToolCallArguments): Promise<ToolExecResult> {
    return {
      output: 'Task done.',
    };
  }
}
