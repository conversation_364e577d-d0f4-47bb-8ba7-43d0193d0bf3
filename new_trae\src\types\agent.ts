/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { LLMResponse, LLMUsage } from './llm';
import { ToolCall, ToolResult } from './tool';

/**
 * Agent execution states
 */
export enum AgentState {
  IDLE = 'idle',
  THINKING = 'thinking',
  CALLING_TOOL = 'calling_tool',
  REFLECTING = 'reflecting',
  COMPLETED = 'completed',
  ERROR = 'error',
}

/**
 * Agent step structure
 */
export interface AgentStep {
  stepNumber: number;
  state: AgentState;
  thought?: string;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
  llmResponse?: LLMResponse;
  reflection?: string;
  error?: string;
  extra?: Record<string, unknown>;
  llmUsage?: LLMUsage;
}

/**
 * Agent execution result
 */
export interface AgentExecution {
  task: string;
  steps: AgentStep[];
  finalResult?: string;
  success: boolean;
  totalTokens?: LLMUsage;
  executionTime: number;
}

/**
 * Agent error class
 */
export class AgentError extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'AgentError';
  }
}
