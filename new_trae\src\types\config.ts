/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { ModelParameters } from './llm';

/**
 * Lakeview configuration
 */
export interface LakeviewConfig {
  modelProvider: string;
  modelName: string;
}

/**
 * Main configuration structure
 */
export interface Config {
  defaultProvider: string;
  maxSteps: number;
  modelProviders: Record<string, ModelParameters>;
  lakeviewConfig?: LakeviewConfig;
  enableLakeview: boolean;
}
