/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { ToolCall, ToolResult } from './tool';

/**
 * LLM usage statistics
 */
export interface LLMUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
}

/**
 * LLM message role types
 */
export type LLMMessageRole = 'system' | 'user' | 'assistant';

/**
 * LLM message structure
 */
export interface LLMMessage {
  role: LLMMessageRole;
  content?: string;
  toolResult?: ToolResult;
}

/**
 * LLM response structure
 */
export interface LLMResponse {
  content: string;
  model: string;
  finishReason: string | null;
  usage: LLMUsage | null;
  toolCalls: ToolCall[] | null;
}

/**
 * Supported LLM providers
 */
export enum LLMProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  AZURE = 'azure',
  OLLAMA = 'ollama',
  OPENROUTER = 'openrouter',
  DOUBAO = 'doubao',
  GOOGLE = 'google',
}

/**
 * Model parameters for LLM configuration
 */
export interface ModelParameters {
  model: string;
  apiKey: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
  parallelToolCalls: boolean;
  maxRetries: number;
  baseUrl?: string;
  apiVersion?: string;
  candidateCount?: number; // Gemini specific
  stopSequences?: string[];
}
