/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

/**
 * Tool parameter definition
 */
export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  enum?: string[];
  properties?: Record<string, ToolParameter>;
  items?: ToolParameter;
}

/**
 * Tool call arguments
 */
export type ToolCallArguments = Record<string, unknown>;

/**
 * Tool call structure
 */
export interface ToolCall {
  callId: string;
  name: string;
  arguments: ToolCallArguments;
}

/**
 * Tool execution result
 */
export interface ToolExecResult {
  output?: string;
  error?: string;
  errorCode?: number;
}

/**
 * Tool result structure
 */
export interface ToolResult {
  callId: string;
  name: string;
  result: ToolExecResult;
}

/**
 * Tool input schema for LLM
 */
export interface ToolInputSchema {
  type: 'object';
  properties: Record<string, unknown>;
  required: string[];
  additionalProperties?: boolean;
}
