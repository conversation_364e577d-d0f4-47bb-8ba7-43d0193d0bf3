/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { LLMMessage, LLMResponse, ModelParameters } from '../types/llm';
import { Tool } from '../tools/base';
import { TrajectoryRecorder } from './trajectory-recorder';

/**
 * Base class for all LLM clients
 */
export abstract class BaseLLMClient {
  protected apiKey: string;
  protected baseUrl?: string;
  protected apiVersion?: string;
  protected trajectoryRecorder?: TrajectoryRecorder;

  constructor(modelParameters: ModelParameters) {
    this.apiKey = modelParameters.apiKey;
    this.baseUrl = modelParameters.baseUrl;
    this.apiVersion = modelParameters.apiVersion;
  }

  /**
   * Set the trajectory recorder for this client
   */
  setTrajectoryRecorder(recorder?: TrajectoryRecorder): void {
    this.trajectoryRecorder = recorder;
  }

  /**
   * Set the chat history
   */
  abstract setChatHistory(messages: LLMMessage[]): void;

  /**
   * Send chat messages to the LLM
   */
  abstract chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory?: boolean
  ): Promise<LLMResponse>;

  /**
   * Check if the current model supports tool calling
   */
  abstract supportsToolCalling(modelParameters: ModelParameters): boolean;

  /**
   * Parse messages to provider-specific format
   */
  protected abstract parseMessages(messages: LLMMessage[]): unknown;

  /**
   * Validate API key
   */
  protected validateApiKey(): void {
    if (!this.apiKey || this.apiKey.trim() === '') {
      throw new Error('API key is required but not provided');
    }
  }
}
