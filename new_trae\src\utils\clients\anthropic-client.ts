/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import Anthropic from '@anthropic-ai/sdk';
import { LLMMessage, LLMResponse, LLMUsage, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { ToolCall } from '../../types/tool';
import { BaseLLMClient } from '../base-client';
import { retryWithAsync } from '../retry-utils';

/**
 * Anthropic client wrapper with tool schema generation
 */
export class AnthropicClient extends BaseLLMClient {
  private client: Anthropic;
  private messageHistory: Anthropic.MessageParam[] = [];
  private systemMessage: string | undefined;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    this.client = new Anthropic({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
    });
  }

  setChatHistory(messages: LLMMessage[]): void {
    const { messages: parsedMessages, systemMessage } = this.parseMessages(messages);
    this.messageHistory = parsedMessages;
    this.systemMessage = systemMessage;
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory: boolean = true
  ): Promise<LLMResponse> {
    const { messages: anthropicMessages, systemMessage } = this.parseMessages(messages);

    const messageHistory = reuseHistory 
      ? [...this.messageHistory, ...anthropicMessages]
      : anthropicMessages;

    let toolSchemas: Anthropic.Tool[] | undefined;
    if (tools && tools.length > 0) {
      toolSchemas = tools.map(tool => ({
        name: tool.getName(),
        description: tool.getDescription(),
        input_schema: tool.getInputSchema(),
      }));
    }

    // Create retry wrapper
    const createMessage = retryWithAsync(
      this.createAnthropicResponse.bind(this),
      modelParameters.maxRetries
    );

    const response = await createMessage(
      messageHistory,
      modelParameters,
      systemMessage || this.systemMessage,
      toolSchemas
    );

    // Update message history
    this.messageHistory = messageHistory;
    if (response.content) {
      this.messageHistory.push({
        role: 'assistant',
        content: response.content,
      });
    }

    // Record trajectory if recorder is available
    if (this.trajectoryRecorder) {
      const llmResponse = this.parseAnthropicResponse(response);
      this.trajectoryRecorder.recordLLMInteraction(
        messages,
        llmResponse,
        'anthropic',
        modelParameters.model,
        tools?.map(t => t.getName())
      );
    }

    return this.parseAnthropicResponse(response);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Claude models support tool calling
    return modelParameters.model.includes('claude');
  }

  protected parseMessages(messages: LLMMessage[]): {
    messages: Anthropic.MessageParam[];
    systemMessage?: string;
  } {
    let systemMessage: string | undefined;
    const anthropicMessages: Anthropic.MessageParam[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        systemMessage = msg.content;
        continue;
      }

      if (msg.toolResult) {
        anthropicMessages.push({
          role: 'user',
          content: [
            {
              type: 'tool_result',
              tool_use_id: msg.toolResult.callId,
              content: msg.toolResult.result.output || msg.toolResult.result.error || '',
            },
          ],
        });
        continue;
      }

      anthropicMessages.push({
        role: msg.role as 'user' | 'assistant',
        content: msg.content || '',
      });
    }

    return { messages: anthropicMessages, systemMessage };
  }

  private async createAnthropicResponse(
    messages: Anthropic.MessageParam[],
    modelParameters: ModelParameters,
    systemMessage?: string,
    tools?: Anthropic.Tool[]
  ): Promise<Anthropic.Message> {
    return this.client.messages.create({
      model: modelParameters.model,
      messages,
      max_tokens: modelParameters.maxTokens,
      system: systemMessage,
      tools,
      temperature: modelParameters.temperature,
      top_p: modelParameters.topP,
      top_k: modelParameters.topK,
    });
  }

  private parseAnthropicResponse(response: Anthropic.Message): LLMResponse {
    let content = '';
    const toolCalls: ToolCall[] = [];

    for (const contentBlock of response.content) {
      if (contentBlock.type === 'text') {
        content += contentBlock.text;
      } else if (contentBlock.type === 'tool_use') {
        toolCalls.push({
          callId: contentBlock.id,
          name: contentBlock.name,
          arguments: contentBlock.input as Record<string, unknown>,
        });
      }
    }

    const usage: LLMUsage | null = response.usage ? {
      inputTokens: response.usage.input_tokens,
      outputTokens: response.usage.output_tokens,
      totalTokens: response.usage.input_tokens + response.usage.output_tokens,
    } : null;

    return {
      content,
      model: response.model,
      finishReason: response.stop_reason,
      usage,
      toolCalls: toolCalls.length > 0 ? toolCalls : null,
    };
  }
}
