/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { AzureOpenAI } from 'openai';
import { LLMMessage, LLMResponse, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { BaseLLMClient } from '../base-client';
import { OpenAIClient } from './openai-client';

/**
 * Azure OpenAI client wrapper
 */
export class AzureClient extends BaseLLMClient {
  private openaiClient: OpenAIClient;
  private azureClient: AzureOpenAI;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    if (!this.baseUrl) {
      throw new Error('base_url is required for AzureClient');
    }

    this.azureClient = new AzureOpenAI({
      apiKey: this.apiKey,
      endpoint: this.baseUrl,
      apiVersion: this.apiVersion || '2024-03-01-preview',
    });

    // Reuse OpenAI client logic with Azure client
    this.openaiClient = new OpenAIClient(modelParameters);
    // Replace the internal client
    (this.openaiClient as any).client = this.azureClient;
  }

  setChatHistory(messages: LLMMessage[]): void {
    this.openaiClient.setChatHistory(messages);
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory?: boolean
  ): Promise<LLMResponse> {
    return this.openaiClient.chat(messages, modelParameters, tools, reuseHistory);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    return this.openaiClient.supportsToolCalling(modelParameters);
  }

  protected parseMessages(messages: LLMMessage[]): unknown {
    return (this.openaiClient as any).parseMessages(messages);
  }
}
