/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import OpenAI from 'openai';
import { LLMMessage, LLMResponse, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { BaseLLMClient } from '../base-client';
import { OpenAIClient } from './openai-client';

/**
 * Doubao client wrapper (uses OpenAI-compatible API)
 */
export class DoubaoClient extends BaseLLMClient {
  private openaiClient: OpenAIClient;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    // Doubao uses OpenAI-compatible API
    const doubaoParams = {
      ...modelParameters,
      baseUrl: modelParameters.baseUrl || 'https://ark.cn-beijing.volces.com/api/v3/',
    };

    this.openaiClient = new OpenAIClient(doubaoParams);
  }

  setChatHistory(messages: LLMMessage[]): void {
    this.openaiClient.setChatHistory(messages);
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory?: boolean
  ): Promise<LLMResponse> {
    return this.openaiClient.chat(messages, modelParameters, tools, reuseHistory);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Doubao models support tool calling
    return modelParameters.model.includes('doubao');
  }

  protected parseMessages(messages: LLMMessage[]): unknown {
    return (this.openaiClient as any).parseMessages(messages);
  }
}
