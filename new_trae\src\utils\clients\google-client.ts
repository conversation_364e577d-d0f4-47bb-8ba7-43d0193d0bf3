/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { GoogleGenerativeAI, GenerativeModel, Content, Part } from '@google/generative-ai';
import { LLMMessage, LLMResponse, LLMUsage, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { ToolCall } from '../../types/tool';
import { BaseLLMClient } from '../base-client';
import { retryWithAsync } from '../retry-utils';

/**
 * Google Gemini client wrapper with tool schema generation
 */
export class GoogleClient extends BaseLLMClient {
  private client: GoogleGenerativeAI;
  private messageHistory: Content[] = [];
  private systemInstruction?: string;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    this.client = new GoogleGenerativeAI(this.apiKey);
  }

  setChatHistory(messages: LLMMessage[]): void {
    const { messages: parsedMessages, systemInstruction } = this.parseMessages(messages);
    this.messageHistory = parsedMessages;
    this.systemInstruction = systemInstruction;
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory: boolean = true
  ): Promise<LLMResponse> {
    const { messages: googleMessages, systemInstruction } = this.parseMessages(messages);

    const currentChatContents = reuseHistory 
      ? [...this.messageHistory, ...googleMessages]
      : googleMessages;

    const model = this.client.getGenerativeModel({
      model: modelParameters.model,
      systemInstruction: systemInstruction || this.systemInstruction,
    });

    let toolSchemas;
    if (tools && tools.length > 0) {
      toolSchemas = tools.map(tool => ({
        name: tool.getName(),
        description: tool.getDescription(),
        parameters: tool.getInputSchema(),
      }));
    }

    // Create retry wrapper
    const generateContent = retryWithAsync(
      this.createGoogleResponse.bind(this),
      modelParameters.maxRetries
    );

    const response = await generateContent(
      model,
      currentChatContents,
      modelParameters,
      toolSchemas
    );

    // Update message history
    this.messageHistory = currentChatContents;
    if (response.response.candidates?.[0]?.content) {
      this.messageHistory.push(response.response.candidates[0].content);
    }

    // Record trajectory if recorder is available
    if (this.trajectoryRecorder) {
      const llmResponse = this.parseGoogleResponse(response.response);
      this.trajectoryRecorder.recordLLMInteraction(
        messages,
        llmResponse,
        'google',
        modelParameters.model,
        tools?.map(t => t.getName())
      );
    }

    return this.parseGoogleResponse(response.response);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Gemini models support function calling
    return modelParameters.model.includes('gemini');
  }

  protected parseMessages(messages: LLMMessage[]): {
    messages: Content[];
    systemInstruction?: string;
  } {
    let systemInstruction: string | undefined;
    const googleMessages: Content[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        systemInstruction = msg.content;
        continue;
      }

      if (msg.toolResult) {
        googleMessages.push({
          role: 'function',
          parts: [{
            functionResponse: {
              name: msg.toolResult.name,
              response: {
                content: msg.toolResult.result.output || msg.toolResult.result.error || '',
              },
            },
          }],
        });
        continue;
      }

      const role = msg.role === 'assistant' ? 'model' : 'user';
      googleMessages.push({
        role,
        parts: [{ text: msg.content || '' }],
      });
    }

    return { messages: googleMessages, systemInstruction };
  }

  private async createGoogleResponse(
    model: GenerativeModel,
    contents: Content[],
    modelParameters: ModelParameters,
    tools?: any[]
  ) {
    const generationConfig = {
      temperature: modelParameters.temperature,
      topP: modelParameters.topP,
      topK: modelParameters.topK,
      maxOutputTokens: modelParameters.maxTokens,
      candidateCount: modelParameters.candidateCount || 1,
      stopSequences: modelParameters.stopSequences,
    };

    if (tools && tools.length > 0) {
      return model.generateContent({
        contents,
        tools: [{ functionDeclarations: tools }],
        generationConfig,
      });
    }

    return model.generateContent({
      contents,
      generationConfig,
    });
  }

  private parseGoogleResponse(response: any): LLMResponse {
    let content = '';
    const toolCalls: ToolCall[] = [];

    if (response.candidates?.[0]?.content?.parts) {
      for (const part of response.candidates[0].content.parts) {
        if (part.text) {
          content += part.text;
        } else if (part.functionCall) {
          toolCalls.push({
            callId: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: part.functionCall.name,
            arguments: part.functionCall.args || {},
          });
        }
      }
    }

    // Google doesn't provide detailed usage info in the same format
    const usage: LLMUsage | null = response.usageMetadata ? {
      inputTokens: response.usageMetadata.promptTokenCount || 0,
      outputTokens: response.usageMetadata.candidatesTokenCount || 0,
      totalTokens: response.usageMetadata.totalTokenCount || 0,
    } : null;

    return {
      content,
      model: 'gemini', // Google doesn't return model name in response
      finishReason: response.candidates?.[0]?.finishReason || null,
      usage,
      toolCalls: toolCalls.length > 0 ? toolCalls : null,
    };
  }
}
