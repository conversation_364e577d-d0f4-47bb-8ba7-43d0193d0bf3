/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import OpenAI from 'openai';
import { LLMMessage, LLMResponse, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { BaseLLMClient } from '../base-client';
import { OpenAIClient } from './openai-client';

/**
 * Ollama client wrapper (uses OpenAI-compatible API)
 */
export class OllamaClient extends BaseLLMClient {
  private openaiClient: OpenAIClient;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);

    // Ollama uses OpenAI-compatible API
    const ollamaParams = {
      ...modelParameters,
      apiKey: modelParameters.apiKey || 'ollama', // Ollama doesn't require real API key
      baseUrl: modelParameters.baseUrl || 'http://localhost:11434/v1',
    };

    this.openaiClient = new OpenAIClient(ollamaParams);
  }

  setChatHistory(messages: LLMMessage[]): void {
    this.openaiClient.setChatHistory(messages);
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory?: boolean
  ): Promise<LLMResponse> {
    return this.openaiClient.chat(messages, modelParameters, tools, reuseHistory);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Some Ollama models support tool calling, but not all
    const supportedModels = ['llama3', 'mistral', 'codellama'];
    return supportedModels.some(model => modelParameters.model.includes(model));
  }

  protected parseMessages(messages: LLMMessage[]): unknown {
    return (this.openaiClient as any).parseMessages(messages);
  }

  protected validateApiKey(): void {
    // Ollama doesn't require API key validation
  }
}
