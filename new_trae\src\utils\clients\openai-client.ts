/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import OpenAI from 'openai';
import { LLMMessage, LLMResponse, LLMUsage, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { ToolCall } from '../../types/tool';
import { BaseLLMClient } from '../base-client';
import { retryWithAsync } from '../retry-utils';

/**
 * OpenAI client wrapper with tool schema generation
 */
export class OpenAIClient extends BaseLLMClient {
  private client: OpenAI;
  private messageHistory: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
    });
  }

  setChatHistory(messages: LLMMessage[]): void {
    this.messageHistory = this.parseMessages(messages);
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory: boolean = true
  ): Promise<LLMResponse> {
    const openaiMessages = this.parseMessages(messages);

    let toolSchemas: OpenAI.Chat.Completions.ChatCompletionTool[] | undefined;
    if (tools && tools.length > 0) {
      toolSchemas = tools.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.getName(),
          description: tool.getDescription(),
          parameters: tool.getInputSchema(),
          strict: true,
        },
      }));
    }

    const apiCallInput: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];
    if (reuseHistory) {
      apiCallInput.push(...this.messageHistory);
    }
    apiCallInput.push(...openaiMessages);

    // Create retry wrapper
    const createCompletion = retryWithAsync(
      this.createOpenAIResponse.bind(this),
      modelParameters.maxRetries
    );

    const response = await createCompletion(apiCallInput, modelParameters, toolSchemas);

    // Update message history
    this.messageHistory = apiCallInput;
    if (response.choices[0]?.message) {
      this.messageHistory.push(response.choices[0].message);
    }

    // Record trajectory if recorder is available
    if (this.trajectoryRecorder) {
      const llmResponse = this.parseOpenAIResponse(response);
      this.trajectoryRecorder.recordLLMInteraction(
        messages,
        llmResponse,
        'openai',
        modelParameters.model,
        tools?.map(t => t.getName())
      );
    }

    return this.parseOpenAIResponse(response);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Most OpenAI models support tool calling
    const supportedModels = [
      'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'
    ];
    return supportedModels.some(model => modelParameters.model.includes(model));
  }

  protected parseMessages(messages: LLMMessage[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      if (msg.toolResult) {
        return {
          role: 'tool' as const,
          content: msg.toolResult.result.output || msg.toolResult.result.error || '',
          tool_call_id: msg.toolResult.callId,
        };
      }

      return {
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content || '',
      };
    });
  }

  private async createOpenAIResponse(
    messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    modelParameters: ModelParameters,
    tools?: OpenAI.Chat.Completions.ChatCompletionTool[]
  ): Promise<OpenAI.Chat.Completions.ChatCompletion> {
    return this.client.chat.completions.create({
      model: modelParameters.model,
      messages,
      tools,
      temperature: modelParameters.temperature,
      top_p: modelParameters.topP,
      max_tokens: modelParameters.maxTokens,
      n: 1,
    });
  }

  private parseOpenAIResponse(response: OpenAI.Chat.Completions.ChatCompletion): LLMResponse {
    const choice = response.choices[0];
    if (!choice) {
      throw new Error('No response choice available');
    }

    const content = choice.message.content || '';
    let toolCalls: ToolCall[] | null = null;

    if (choice.message.tool_calls) {
      toolCalls = choice.message.tool_calls.map(toolCall => ({
        callId: toolCall.id,
        name: toolCall.function.name,
        arguments: JSON.parse(toolCall.function.arguments) as Record<string, unknown>,
      }));
    }

    const usage: LLMUsage | null = response.usage ? {
      inputTokens: response.usage.prompt_tokens,
      outputTokens: response.usage.completion_tokens,
      totalTokens: response.usage.total_tokens,
    } : null;

    return {
      content,
      model: response.model,
      finishReason: choice.finish_reason,
      usage,
      toolCalls,
    };
  }
}
