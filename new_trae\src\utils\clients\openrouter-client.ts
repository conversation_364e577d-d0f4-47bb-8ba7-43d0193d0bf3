/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import OpenAI from 'openai';
import { LLMMessage, LLMResponse, ModelParameters } from '../../types/llm';
import { Tool } from '../../tools/base';
import { BaseLLMClient } from '../base-client';
import { OpenAIClient } from './openai-client';

/**
 * OpenRouter client wrapper (uses OpenAI-compatible API)
 */
export class OpenRouterClient extends BaseLLMClient {
  private openaiClient: OpenAIClient;

  constructor(modelParameters: ModelParameters) {
    super(modelParameters);
    this.validateApiKey();

    // OpenRouter uses OpenAI-compatible API
    const openRouterParams = {
      ...modelParameters,
      baseUrl: modelParameters.baseUrl || 'https://openrouter.ai/api/v1',
    };

    this.openaiClient = new OpenAIClient(openRouterParams);

    // Set additional headers for OpenRouter
    const client = (this.openaiClient as any).client as OpenAI;
    client.defaultHeaders = {
      ...client.defaultHeaders,
      'HTTP-Referer': process.env.OPENROUTER_SITE_URL || 'https://github.com/bytedance/trae-agent',
      'X-Title': process.env.OPENROUTER_SITE_NAME || 'Trae Agent',
    };
  }

  setChatHistory(messages: LLMMessage[]): void {
    this.openaiClient.setChatHistory(messages);
  }

  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[],
    reuseHistory?: boolean
  ): Promise<LLMResponse> {
    return this.openaiClient.chat(messages, modelParameters, tools, reuseHistory);
  }

  supportsToolCalling(modelParameters: ModelParameters): boolean {
    // Most models on OpenRouter support tool calling
    return true;
  }

  protected parseMessages(messages: LLMMessage[]): unknown {
    return (this.openaiClient as any).parseMessages(messages);
  }
}
