/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { Config, LakeviewConfig } from '../types/config';
import { ModelParameters, LLMProvider } from '../types/llm';

/**
 * Default model parameters for different providers
 */
const DEFAULT_MODEL_PARAMETERS: Record<string, Partial<ModelParameters>> = {
  [LLMProvider.OPENAI]: {
    model: 'gpt-4o',
    maxTokens: 128000,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: true,
    maxRetries: 10,
    baseUrl: 'https://api.openai.com/v1',
  },
  [LLMProvider.ANTHROPIC]: {
    model: 'claude-sonnet-4-20250514',
    maxTokens: 4096,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: false,
    maxRetries: 10,
    baseUrl: 'https://api.anthropic.com',
  },
  [LLMProvider.GOOGLE]: {
    model: 'gemini-2.5-flash',
    maxTokens: 120000,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: false,
    maxRetries: 10,
  },
  [LLMProvider.AZURE]: {
    model: 'gpt-4o',
    maxTokens: 4096,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: true,
    maxRetries: 10,
    apiVersion: '2024-03-01-preview',
  },
  [LLMProvider.OLLAMA]: {
    model: 'llama3',
    maxTokens: 4096,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: false,
    maxRetries: 10,
    baseUrl: 'http://localhost:11434/v1',
    apiKey: 'ollama',
  },
  [LLMProvider.OPENROUTER]: {
    model: 'openai/gpt-4o',
    maxTokens: 4096,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: true,
    maxRetries: 10,
    baseUrl: 'https://openrouter.ai/api/v1',
  },
  [LLMProvider.DOUBAO]: {
    model: 'doubao-seed-1.6',
    maxTokens: 8192,
    temperature: 0.5,
    topP: 1,
    topK: 0,
    parallelToolCalls: false,
    maxRetries: 20,
  },
};

/**
 * Get API key from environment variables
 */
function getApiKeyFromEnv(provider: string): string {
  const envKeyMap: Record<string, string> = {
    [LLMProvider.OPENAI]: 'OPENAI_API_KEY',
    [LLMProvider.ANTHROPIC]: 'ANTHROPIC_API_KEY',
    [LLMProvider.GOOGLE]: 'GOOGLE_API_KEY',
    [LLMProvider.AZURE]: 'AZURE_API_KEY',
    [LLMProvider.OLLAMA]: 'OLLAMA_API_KEY',
    [LLMProvider.OPENROUTER]: 'OPENROUTER_API_KEY',
    [LLMProvider.DOUBAO]: 'DOUBAO_API_KEY',
  };

  const envKey = envKeyMap[provider];
  if (!envKey) {
    throw new Error(`Unknown provider: ${provider}`);
  }

  return process.env[envKey] || '';
}

/**
 * Get base URL from environment variables
 */
function getBaseUrlFromEnv(provider: string): string | undefined {
  const envKeyMap: Record<string, string> = {
    [LLMProvider.OPENAI]: 'OPENAI_BASE_URL',
    [LLMProvider.DOUBAO]: 'DOUBAO_BASE_URL',
  };

  const envKey = envKeyMap[provider];
  return envKey ? process.env[envKey] : undefined;
}

/**
 * Create model parameters with defaults and environment overrides
 */
function createModelParameters(
  provider: string,
  configParams: Partial<ModelParameters> = {},
  overrides: Partial<ModelParameters> = {}
): ModelParameters {
  const defaults = DEFAULT_MODEL_PARAMETERS[provider] || {};
  const apiKey = overrides.apiKey || configParams.apiKey || getApiKeyFromEnv(provider);
  const baseUrl = overrides.baseUrl || configParams.baseUrl || getBaseUrlFromEnv(provider) || defaults.baseUrl;

  return {
    model: overrides.model || configParams.model || defaults.model || 'gpt-4o',
    apiKey,
    maxTokens: overrides.maxTokens || configParams.maxTokens || defaults.maxTokens || 4096,
    temperature: overrides.temperature ?? configParams.temperature ?? defaults.temperature ?? 0.5,
    topP: overrides.topP ?? configParams.topP ?? defaults.topP ?? 1,
    topK: overrides.topK ?? configParams.topK ?? defaults.topK ?? 0,
    parallelToolCalls: overrides.parallelToolCalls ?? configParams.parallelToolCalls ?? defaults.parallelToolCalls ?? false,
    maxRetries: overrides.maxRetries || configParams.maxRetries || defaults.maxRetries || 10,
    baseUrl,
    apiVersion: overrides.apiVersion || configParams.apiVersion || defaults.apiVersion,
    candidateCount: overrides.candidateCount || configParams.candidateCount || defaults.candidateCount,
    stopSequences: overrides.stopSequences || configParams.stopSequences || defaults.stopSequences,
  };
}

/**
 * Load configuration from file
 */
export function loadConfigFromFile(configPath: string): Partial<Config> {
  try {
    if (!fs.existsSync(configPath)) {
      return {};
    }

    const configContent = fs.readFileSync(configPath, 'utf-8');
    return JSON.parse(configContent) as Partial<Config>;
  } catch (error) {
    console.warn(`Failed to load config from ${configPath}:`, error);
    return {};
  }
}

/**
 * Load and create complete configuration
 */
export function loadConfig(options: {
  configFile?: string;
  provider?: string;
  model?: string;
  modelBaseUrl?: string;
  apiKey?: string;
  maxSteps?: number;
} = {}): Config {
  const {
    configFile = 'trae_config.json',
    provider,
    model,
    modelBaseUrl,
    apiKey,
    maxSteps = 20,
  } = options;

  // Load from file
  const fileConfig = loadConfigFromFile(configFile);

  // Determine default provider
  const defaultProvider = provider || fileConfig.defaultProvider || LLMProvider.ANTHROPIC;

  // Create model providers configuration
  const modelProviders: Record<string, ModelParameters> = {};

  // Add all providers from file config
  if (fileConfig.modelProviders) {
    for (const [providerName, providerConfig] of Object.entries(fileConfig.modelProviders)) {
      modelProviders[providerName] = createModelParameters(providerName, providerConfig);
    }
  }

  // Override or add the specified provider
  if (provider) {
    const overrides: Partial<ModelParameters> = {};
    if (model) overrides.model = model;
    if (modelBaseUrl) overrides.baseUrl = modelBaseUrl;
    if (apiKey) overrides.apiKey = apiKey;

    modelProviders[provider] = createModelParameters(
      provider,
      fileConfig.modelProviders?.[provider],
      overrides
    );
  }

  // Ensure default provider exists
  if (!modelProviders[defaultProvider]) {
    const overrides: Partial<ModelParameters> = {};
    if (model) overrides.model = model;
    if (modelBaseUrl) overrides.baseUrl = modelBaseUrl;
    if (apiKey) overrides.apiKey = apiKey;

    modelProviders[defaultProvider] = createModelParameters(defaultProvider, {}, overrides);
  }

  return {
    defaultProvider,
    maxSteps,
    modelProviders,
    lakeviewConfig: fileConfig.lakeviewConfig,
    enableLakeview: fileConfig.enableLakeview ?? true,
  };
}

export { Config, ModelParameters, LakeviewConfig };
