/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as path from 'path';
import * as os from 'os';

/**
 * Local storage path for Trae Agent
 */
export const LOCAL_STORAGE_PATH = path.join(os.homedir(), '.trae-agent');

/**
 * CKG database path
 */
export const CKG_DATABASE_PATH = path.join(LOCAL_STORAGE_PATH, 'ckg');

/**
 * CKG database expiry time (1 week in seconds)
 */
export const CKG_DATABASE_EXPIRY_TIME = 60 * 60 * 24 * 7;

/**
 * Get CKG database path for a specific codebase
 */
export function getCkgDatabasePath(codebaseSnapshotHash: string): string {
  return path.join(CKG_DATABASE_PATH, `${codebaseSnapshotHash}.db`);
}

/**
 * Default trajectory directory
 */
export const DEFAULT_TRAJECTORY_DIR = 'trajectories';

/**
 * Supported file extensions for code analysis
 */
export const SUPPORTED_CODE_EXTENSIONS = [
  '.js', '.ts', '.jsx', '.tsx',
  '.py', '.pyx', '.pyi',
  '.java', '.kt',
  '.cpp', '.cc', '.cxx', '.c', '.h', '.hpp',
  '.cs',
  '.go',
  '.rs',
  '.rb',
  '.php',
  '.swift',
  '.scala',
  '.clj', '.cljs',
  '.sh', '.bash',
  '.sql',
  '.html', '.htm',
  '.css', '.scss', '.sass',
  '.json', '.yaml', '.yml',
  '.xml',
  '.md', '.rst',
  '.txt',
];

/**
 * Maximum file size for processing (in bytes)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Default timeout for tool execution (in milliseconds)
 */
export const DEFAULT_TOOL_TIMEOUT = 120000; // 2 minutes

/**
 * Default timeout for LLM requests (in milliseconds)
 */
export const DEFAULT_LLM_TIMEOUT = 60000; // 1 minute
