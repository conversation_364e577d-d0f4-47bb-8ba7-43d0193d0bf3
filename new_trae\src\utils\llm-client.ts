/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { LLMMessage, LLMResponse, LLMProvider, ModelParameters } from '../types/llm';
import { Tool } from '../tools/base';
import { BaseLLMClient } from './base-client';
import { TrajectoryRecorder } from './trajectory-recorder';

/**
 * Main LLM client that supports multiple providers
 */
export class LLMClient {
  private client: BaseLLMClient;
  private modelParameters: ModelParameters;
  private maxSteps: number;

  constructor(
    provider: LLMProvider,
    modelParameters: ModelParameters,
    maxSteps: number = 20
  ) {
    this.modelParameters = modelParameters;
    this.maxSteps = maxSteps;

    // Create provider-specific client
    switch (provider) {
      case LLMProvider.OPENAI:
        this.client = this.createOpenAIClient(modelParameters);
        break;
      case LLMProvider.ANTHROPIC:
        this.client = this.createAnthropicClient(modelParameters);
        break;
      case LLMProvider.AZURE:
        this.client = this.createAzureClient(modelParameters);
        break;
      case LLMProvider.OPENROUTER:
        this.client = this.createOpenRouterClient(modelParameters);
        break;
      case LLMProvider.DOUBAO:
        this.client = this.createDoubaoClient(modelParameters);
        break;
      case LLMProvider.OLLAMA:
        this.client = this.createOllamaClient(modelParameters);
        break;
      case LLMProvider.GOOGLE:
        this.client = this.createGoogleClient(modelParameters);
        break;
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Get the model parameters used by this client
   */
  getModelParameters(): ModelParameters {
    return this.modelParameters;
  }

  /**
   * Get the max steps used by this client
   */
  getMaxSteps(): number {
    return this.maxSteps;
  }

  /**
   * Set the trajectory recorder for the underlying client
   */
  setTrajectoryRecorder(recorder?: TrajectoryRecorder): void {
    this.client.setTrajectoryRecorder(recorder);
  }

  /**
   * Set the chat history
   */
  setChatHistory(messages: LLMMessage[]): void {
    this.client.setChatHistory(messages);
  }

  /**
   * Send chat messages to the LLM
   */
  async chat(
    messages: LLMMessage[],
    modelParameters: ModelParameters,
    tools?: Tool[]
  ): Promise<LLMResponse> {
    return this.client.chat(messages, modelParameters, tools);
  }

  /**
   * Check if the current model supports tool calling
   */
  supportsToolCalling(): boolean {
    return this.client.supportsToolCalling(this.modelParameters);
  }

  // Provider-specific client creation methods
  private createOpenAIClient(modelParameters: ModelParameters): BaseLLMClient {
    // Dynamic import to avoid loading all dependencies
    const { OpenAIClient } = require('./clients/openai-client');
    return new OpenAIClient(modelParameters);
  }

  private createAnthropicClient(modelParameters: ModelParameters): BaseLLMClient {
    const { AnthropicClient } = require('./clients/anthropic-client');
    return new AnthropicClient(modelParameters);
  }

  private createAzureClient(modelParameters: ModelParameters): BaseLLMClient {
    const { AzureClient } = require('./clients/azure-client');
    return new AzureClient(modelParameters);
  }

  private createOpenRouterClient(modelParameters: ModelParameters): BaseLLMClient {
    const { OpenRouterClient } = require('./clients/openrouter-client');
    return new OpenRouterClient(modelParameters);
  }

  private createDoubaoClient(modelParameters: ModelParameters): BaseLLMClient {
    const { DoubaoClient } = require('./clients/doubao-client');
    return new DoubaoClient(modelParameters);
  }

  private createOllamaClient(modelParameters: ModelParameters): BaseLLMClient {
    const { OllamaClient } = require('./clients/ollama-client');
    return new OllamaClient(modelParameters);
  }

  private createGoogleClient(modelParameters: ModelParameters): BaseLLMClient {
    const { GoogleClient } = require('./clients/google-client');
    return new GoogleClient(modelParameters);
  }
}

export { LLMProvider };
