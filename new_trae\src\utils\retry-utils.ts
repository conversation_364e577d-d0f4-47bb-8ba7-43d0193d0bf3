/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

/**
 * Retry decorator with randomized backoff
 */
export function retryWith<T extends (...args: any[]) => any>(
  func: T,
  maxRetries: number = 3
): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return func(...args);
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          // Last attempt, re-throw the error
          throw error;
        }

        const sleepTime = Math.floor(Math.random() * 28) + 3; // 3-30 seconds
        console.warn(
          `API call failed: ${lastError.message}. Will sleep for ${sleepTime} seconds and retry.`
        );

        // Sleep synchronously (for simplicity, in real implementation you might want async)
        const start = Date.now();
        while (Date.now() - start < sleepTime * 1000) {
          // Busy wait
        }
      }
    }

    throw lastError!;
  }) as T;
}

/**
 * Async retry decorator with randomized backoff
 */
export function retryWithAsync<T extends (...args: any[]) => Promise<any>>(
  func: T,
  maxRetries: number = 3
): T {
  return (async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> => {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await func(...args);
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          // Last attempt, re-throw the error
          throw error;
        }

        const sleepTime = Math.floor(Math.random() * 28) + 3; // 3-30 seconds
        console.warn(
          `API call failed: ${lastError.message}. Will sleep for ${sleepTime} seconds and retry.`
        );

        await new Promise(resolve => setTimeout(resolve, sleepTime * 1000));
      }
    }

    throw lastError!;
  }) as T;
}

/**
 * Simple sleep function
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
